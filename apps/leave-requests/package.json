{"name": "leave-requests", "version": "0.0.1", "type": "module", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "typecheck": "tsc --noEmit", "db:generate": "drizzle-kit generate --config drizzle.config.ts", "db:migrate": "drizzle-kit migrate --config drizzle.config.ts", "db:push": "drizzle-kit push --config drizzle.config.ts", "db:studio": "drizzle-kit studio --config drizzle.config.ts", "db:drop": "drizzle-kit drop --config drizzle.config.ts", "db:check": "drizzle-kit check --config drizzle.config.ts", "db:up": "drizzle-kit up --config drizzle.config.ts", "db:down": "drizzle-kit down --config drizzle.config.ts", "db:create-migration": "drizzle-kit generate --config drizzle.config.ts --name", "db:reset": "drizzle-kit drop --config drizzle.config.ts && drizzle-kit push --config drizzle.config.ts"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@tanstack/react-table": "^8.21.3", "@workspace/supabase": "workspace:*", "@workspace/ui": "workspace:*", "clsx": "^2.1.1", "date-fns": "^4.1.0", "drizzle-orm": "^0.44.5", "lucide-react": "^0.475.0", "next": "^15.2.3", "next-themes": "^0.4.4", "nodemailer": "^7.0.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "resend": "^6.0.1", "sonner": "^2.0.6", "zod": "^4.0.5"}, "devDependencies": {"@types/node": "^20", "@types/nodemailer": "^7.0.1", "@types/pg": "^8.15.5", "@types/react": "^19", "@types/react-dom": "^19", "@workspace/eslint-config": "workspace:^", "@workspace/typescript-config": "workspace:*", "drizzle-kit": "^0.31.4", "pg": "^8.16.3", "typescript": "^5.7.3"}}