{"id": "cb962b9c-b77b-4a9d-9f1c-1cbf3aabe7c8", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.addresses": {"name": "addresses", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "address_line": {"name": "address_line", "type": "text", "primaryKey": false, "notNull": true}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "text", "primaryKey": false, "notNull": false}, "postal_code": {"name": "postal_code", "type": "text", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": false}, "is_primary": {"name": "is_primary", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"one_primary_address_per_user": {"name": "one_primary_address_per_user", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "uuid_ops"}], "isUnique": true, "where": "(is_primary = true)", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"addresses_user_id_fkey": {"name": "addresses_user_id_fkey", "tableFrom": "addresses", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.bonus_leave_grants": {"name": "bonus_leave_grants", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "year": {"name": "year", "type": "integer", "primaryKey": false, "notNull": true}, "days_granted": {"name": "days_granted", "type": "integer", "primaryKey": false, "notNull": true}, "days_used": {"name": "days_used", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "reason": {"name": "reason", "type": "text", "primaryKey": false, "notNull": false}, "granted_by": {"name": "granted_by", "type": "uuid", "primaryKey": false, "notNull": false}, "granted_at": {"name": "granted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_bonus_leave_grants_granted_by": {"name": "idx_bonus_leave_grants_granted_by", "columns": [{"expression": "granted_by", "isExpression": false, "asc": true, "nulls": "last", "opclass": "uuid_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_bonus_leave_grants_user_id": {"name": "idx_bonus_leave_grants_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "uuid_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_bonus_leave_grants_year": {"name": "idx_bonus_leave_grants_year", "columns": [{"expression": "year", "isExpression": false, "asc": true, "nulls": "last", "opclass": "int4_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"bonus_leave_grants_user_id_fkey": {"name": "bonus_leave_grants_user_id_fkey", "tableFrom": "bonus_leave_grants", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "bonus_leave_grants_granted_by_fkey": {"name": "bonus_leave_grants_granted_by_fkey", "tableFrom": "bonus_leave_grants", "tableTo": "users", "columnsFrom": ["granted_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"Users can view own bonus leave grants": {"name": "Users can view own bonus leave grants", "as": "PERMISSIVE", "for": "SELECT", "to": ["public"], "using": "(auth.uid() = user_id)"}, "Admins can view all bonus leave grants": {"name": "Admins can view all bonus leave grants", "as": "PERMISSIVE", "for": "SELECT", "to": ["public"]}, "Admins can insert bonus leave grants": {"name": "Admins can insert bonus leave grants", "as": "PERMISSIVE", "for": "INSERT", "to": ["public"]}, "Admins can update bonus leave grants": {"name": "Admins can update bonus leave grants", "as": "PERMISSIVE", "for": "UPDATE", "to": ["public"]}, "Admins can delete bonus leave grants": {"name": "Admins can delete bonus leave grants", "as": "PERMISSIVE", "for": "DELETE", "to": ["public"]}}, "checkConstraints": {"bonus_leave_grants_year_check": {"name": "bonus_leave_grants_year_check", "value": "(year >= 2020) AND (year <= 2030)"}, "bonus_leave_grants_days_granted_check": {"name": "bonus_leave_grants_days_granted_check", "value": "days_granted > 0"}, "bonus_leave_grants_days_used_check": {"name": "bonus_leave_grants_days_used_check", "value": "days_used >= 0"}, "bonus_leave_grants_check": {"name": "bonus_leave_grants_check", "value": "days_used <= days_granted"}}, "isRLSEnabled": false}, "public.company_settings": {"name": "company_settings", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "carryover_expiry_day": {"name": "carryover_expiry_day", "type": "integer", "primaryKey": false, "notNull": true}, "carryover_expiry_month": {"name": "carryover_expiry_month", "type": "integer", "primaryKey": false, "notNull": true}, "tenure_accrual_rules": {"name": "tenure_accrual_rules", "type": "jsonb", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"Allow read to all authenticated users": {"name": "Allow read to all authenticated users", "as": "PERMISSIVE", "for": "SELECT", "to": ["public"], "using": "(auth.role() = 'authenticated'::text)"}, "Admins can modify company_settings": {"name": "Admins can modify company_settings", "as": "PERMISSIVE", "for": "ALL", "to": ["public"]}, "Service role can modify": {"name": "Service role can modify", "as": "PERMISSIVE", "for": "ALL", "to": ["public"]}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.extended_absences": {"name": "extended_absences", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "start_date": {"name": "start_date", "type": "date", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "date", "primaryKey": false, "notNull": true}, "reason": {"name": "reason", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"extended_absences_user_id_fkey": {"name": "extended_absences_user_id_fkey", "tableFrom": "extended_absences", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"Users can view their own extended absences": {"name": "Users can view their own extended absences", "as": "PERMISSIVE", "for": "SELECT", "to": ["public"], "using": "(auth.uid() = user_id)"}, "Admins can manage all extended absences": {"name": "Admins can manage all extended absences", "as": "PERMISSIVE", "for": "ALL", "to": ["public"]}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.leave_requests": {"name": "leave_requests", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "leave_type_id": {"name": "leave_type_id", "type": "integer", "primaryKey": false, "notNull": true}, "projects": {"name": "projects", "type": "jsonb", "primaryKey": false, "notNull": false}, "internal_notifications": {"name": "internal_notifications", "type": "uuid[]", "primaryKey": false, "notNull": false}, "external_notifications": {"name": "external_notifications", "type": "text[]", "primaryKey": false, "notNull": false}, "current_manager_id": {"name": "current_manager_id", "type": "uuid", "primaryKey": false, "notNull": false}, "backup_id": {"name": "backup_id", "type": "uuid", "primaryKey": false, "notNull": false}, "start_date": {"name": "start_date", "type": "date", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "date", "primaryKey": false, "notNull": false}, "is_half_day": {"name": "is_half_day", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "half_day_type": {"name": "half_day_type", "type": "text", "primaryKey": false, "notNull": false}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": false}, "emergency_contact": {"name": "emergency_contact", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "approval_notes": {"name": "approval_notes", "type": "text", "primaryKey": false, "notNull": false}, "cancel_reason": {"name": "cancel_reason", "type": "text", "primaryKey": false, "notNull": false}, "approved_by_id": {"name": "approved_by_id", "type": "uuid", "primaryKey": false, "notNull": false}, "approved_at": {"name": "approved_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "canceled_at": {"name": "canceled_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_leave_requests_leave_type_id": {"name": "idx_leave_requests_leave_type_id", "columns": [{"expression": "leave_type_id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "int4_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_leave_requests_start_date": {"name": "idx_leave_requests_start_date", "columns": [{"expression": "start_date", "isExpression": false, "asc": true, "nulls": "last", "opclass": "date_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_leave_requests_status": {"name": "idx_leave_requests_status", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_leave_requests_user_id": {"name": "idx_leave_requests_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "uuid_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"leave_requests_leave_type_id_fkey": {"name": "leave_requests_leave_type_id_fkey", "tableFrom": "leave_requests", "tableTo": "leave_types", "columnsFrom": ["leave_type_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "leave_requests_current_manager_id_fkey": {"name": "leave_requests_current_manager_id_fkey", "tableFrom": "leave_requests", "tableTo": "users", "columnsFrom": ["current_manager_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "leave_requests_backup_id_fkey": {"name": "leave_requests_backup_id_fkey", "tableFrom": "leave_requests", "tableTo": "users", "columnsFrom": ["backup_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "leave_requests_approved_by_id_fkey": {"name": "leave_requests_approved_by_id_fkey", "tableFrom": "leave_requests", "tableTo": "users", "columnsFrom": ["approved_by_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "leave_requests_user_id_fkey": {"name": "leave_requests_user_id_fkey", "tableFrom": "leave_requests", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"Users can view their own leave requests": {"name": "Users can view their own leave requests", "as": "PERMISSIVE", "for": "SELECT", "to": ["public"], "using": "(auth.uid() = user_id)"}, "Users can insert their own leave requests": {"name": "Users can insert their own leave requests", "as": "PERMISSIVE", "for": "INSERT", "to": ["public"]}, "Service role can do everything": {"name": "Service role can do everything", "as": "PERMISSIVE", "for": "ALL", "to": ["public"]}, "Admins and managers can view and update all leave requests": {"name": "Admins and managers can view and update all leave requests", "as": "PERMISSIVE", "for": "ALL", "to": ["public"]}, "Users can update their own pending leave requests": {"name": "Users can update their own pending leave requests", "as": "PERMISSIVE", "for": "UPDATE", "to": ["public"]}}, "checkConstraints": {"leave_requests_half_day_type_check": {"name": "leave_requests_half_day_type_check", "value": "half_day_type = ANY (ARRAY['morning'::text, 'afternoon'::text])"}, "leave_requests_status_check": {"name": "leave_requests_status_check", "value": "status = ANY (ARRAY['pending'::text, 'approved'::text, 'rejected'::text, 'canceled'::text])"}}, "isRLSEnabled": false}, "public.leave_types": {"name": "leave_types", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_paid": {"name": "is_paid", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "supports_carryover": {"name": "supports_carryover", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "supports_half_day": {"name": "supports_half_day", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "quota": {"name": "quota", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"leave_types_name_key": {"name": "leave_types_name_key", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {"Allow read to all authenticated users": {"name": "Allow read to all authenticated users", "as": "PERMISSIVE", "for": "SELECT", "to": ["public"], "using": "(auth.role() = 'authenticated'::text)"}, "Admins can modify leave_types": {"name": "Admins can modify leave_types", "as": "PERMISSIVE", "for": "ALL", "to": ["public"]}, "Service role can modify": {"name": "Service role can modify", "as": "PERMISSIVE", "for": "ALL", "to": ["public"]}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.project_assignments": {"name": "project_assignments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "project_id": {"name": "project_id", "type": "uuid", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'developer'"}, "is_lead": {"name": "is_lead", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "start_date": {"name": "start_date", "type": "date", "primaryKey": false, "notNull": true, "default": "CURRENT_DATE"}, "end_date": {"name": "end_date", "type": "date", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'active'"}, "assigned_by": {"name": "assigned_by", "type": "uuid", "primaryKey": false, "notNull": false}, "assigned_at": {"name": "assigned_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"project_assignments_user_id_fkey": {"name": "project_assignments_user_id_fkey", "tableFrom": "project_assignments", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "project_assignments_project_id_fkey": {"name": "project_assignments_project_id_fkey", "tableFrom": "project_assignments", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "project_assignments_assigned_by_fkey": {"name": "project_assignments_assigned_by_fkey", "tableFrom": "project_assignments", "tableTo": "users", "columnsFrom": ["assigned_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"admin_crud_project_assignments": {"name": "admin_crud_project_assignments", "as": "PERMISSIVE", "for": "ALL", "to": ["public"], "using": "(EXISTS ( SELECT 1\n   FROM users u\n  WHERE ((u.id = auth.uid()) AND (u.role = 'admin'::text))))", "withCheck": "(EXISTS ( SELECT 1\n   FROM users u\n  WHERE ((u.id = auth.uid()) AND (u.role = 'admin'::text))))"}, "user_crud_own_assignments": {"name": "user_crud_own_assignments", "as": "PERMISSIVE", "for": "ALL", "to": ["public"]}}, "checkConstraints": {"valid_date_range": {"name": "valid_date_range", "value": "(end_date IS NULL) OR (end_date >= start_date)"}}, "isRLSEnabled": false}, "public.projects": {"name": "projects", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_billable": {"name": "is_billable", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"projects_name_key": {"name": "projects_name_key", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {"Anyone can view projects": {"name": "Anyone can view projects", "as": "PERMISSIVE", "for": "SELECT", "to": ["public"], "using": "true"}, "Admins can manage projects": {"name": "Admins can manage projects", "as": "PERMISSIVE", "for": "ALL", "to": ["public"]}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.signup_email_domains": {"name": "signup_email_domains", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "domain": {"name": "domain", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "signup_email_domain_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "reason": {"name": "reason", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"signup_email_domains_domain_key": {"name": "signup_email_domains_domain_key", "nullsNotDistinct": false, "columns": ["domain"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "full_name": {"name": "full_name", "type": "text", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "default": "'employee'"}, "start_date": {"name": "start_date", "type": "date", "primaryKey": false, "notNull": false}, "end_date": {"name": "end_date", "type": "date", "primaryKey": false, "notNull": false}, "gender": {"name": "gender", "type": "text", "primaryKey": false, "notNull": false}, "position": {"name": "position", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "date_of_birth": {"name": "date_of_birth", "type": "date", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "manager_id": {"name": "manager_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"users_manager_id_fkey": {"name": "users_manager_id_fkey", "tableFrom": "users", "tableTo": "users", "columnsFrom": ["manager_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_key": {"name": "users_email_key", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {"Admins can perform all actions on users": {"name": "Admins can perform all actions on users", "as": "PERMISSIVE", "for": "ALL", "to": ["public"], "using": "(get_user_role(auth.uid()) = 'admin'::text)", "withCheck": "(get_user_role(auth.uid()) = 'admin'::text)"}, "Authenticated users can view other users": {"name": "Authenticated users can view other users", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"]}, "Users can update their own profile": {"name": "Users can update their own profile", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"]}}, "checkConstraints": {"users_role_check": {"name": "users_role_check", "value": "role = ANY (ARRAY['employee'::text, 'manager'::text, 'admin'::text])"}}, "isRLSEnabled": false}}, "enums": {"public.signup_email_domain_type": {"name": "signup_email_domain_type", "schema": "public", "values": ["allow", "deny"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}