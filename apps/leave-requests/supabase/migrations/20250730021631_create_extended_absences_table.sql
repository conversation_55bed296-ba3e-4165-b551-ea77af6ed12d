-- Migration: 20250731000000_create_extended_absences_table.sql
CREATE TABLE public.extended_absences (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES public.users(id) ON DELETE CASCADE,
  start_date date NOT NULL,
  end_date date NOT NULL,
  reason text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE public.extended_absences ENABLE ROW LEVEL SECURITY;

-- Add RLS policies
CREATE POLICY "Users can view their own extended absences" ON public.extended_absences
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "<PERSON><PERSON> can manage all extended absences" ON public.extended_absences
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE users.id = auth.uid() AND users.role = 'admin'
    )
  );
