"use client";
import { ColumnDef } from "@tanstack/react-table";
import type { User } from "@/types";
import { DataTableColumnHeader } from "./data-table-column-header";
import { DataTableRowActions } from "./data-table-row-actions";
import { Badge } from "@workspace/ui/components/badge";



const roleColors: Record<string, "default" | "secondary" | "destructive"> = {
  employee: "default",
  manager: "secondary",
  admin: "destructive",
};
const genderColors: Record<string, "default" | "secondary"> = {
  male: "default",
  female: "secondary"
};

export const columns: ColumnDef<User>[] = [
  {
    accessorKey: "email",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Email" />,
    enableSorting: true,
  },
  {
    accessorKey: "full_name",
    header: "Name",
  },
  {
    accessorKey: "role",
    header: "Role",
    filterFn: (row, columnId, filterValue) => {
      if (!filterValue || filterValue.length === 0) return true;
      return filterValue.includes(row.getValue(columnId));
    },
    enableSorting: false,
    cell: ({ row }) => {
      const role = row.original.role || "";
      if (!role) return null;
      return (
        <Badge variant={roleColors[role] || "default"}>
          {role.charAt(0).toUpperCase() + role.slice(1)}
        </Badge>
      );
    },
  },
  {
    accessorKey: "position",
    header: "Position",
  },
  {
    accessorKey: "gender",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Gender" />,
    filterFn: (row, columnId, filterValue) => {
      if (!filterValue || filterValue.length === 0) return true;
      return filterValue.includes(row.getValue(columnId));
    },
    cell: ({ row }) => {
      const gender = row.original.gender || "";
      if (!gender) return null;
      const genderLower = gender.toLowerCase();
      return (
        <Badge variant={genderColors[genderLower] || "default"}>
          {gender.charAt(0).toUpperCase() + gender.slice(1)}
        </Badge>
      );
    },
  },
  {
    accessorKey: "start_date",
    header: "Start Date",
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row, table }) => <DataTableRowActions row={row} onUpdate={() => (table.options.meta as any)?.onUpdate?.()} />,
    enableSorting: false,
    enableHiding: false,
  },
]; 