# Database Setup with Dr<PERSON>zle ORM

This directory contains the database configuration and schema definitions using Drizzle ORM with PostgreSQL.

## Files

- `index.ts` - Database connection and exports
- `schema.ts` - Database schema definitions
- `README.md` - This file

## Environment Variables

Make sure to set the following environment variable:

```bash
DATABASE_URL="postgresql://username:password@localhost:5432/leave_requests_db"
```

## Available Scripts

- `pnpm db:generate` - Generate migration files from schema changes
- `pnpm db:migrate` - Run pending migrations
- `pnpm db:push` - Push schema changes directly to database (for development)
- `pnpm db:studio` - Open Drizzle Studio (database GUI)

## Usage

```typescript
import { db, users, leaveRequests } from '@/lib/db';

// Example query
const allUsers = await db.select().from(users);

// Example insert
const newUser = await db.insert(users).values({
  email: '<EMAIL>',
  name: '<PERSON>'
}).returning();
```

## Schema

The current schema includes:
- `users` - User information
- `leaveRequests` - Leave request records

You can extend the schema by adding more tables to `schema.ts`.

